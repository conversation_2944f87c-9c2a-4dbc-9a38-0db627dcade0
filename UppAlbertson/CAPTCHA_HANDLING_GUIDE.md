# CAPTCHA Handling Guide for OTP Extraction

## 🚨 Problem Overview

When extracting OTP codes from YOPmail during automated testing, you may encounter CAPTCHA challenges that prevent the automation from proceeding. This guide provides multiple strategies to handle this issue.

## 🔍 CAPTCHA Detection

The enhanced step definitions now automatically detect CAPTCHA using:

1. **Element Selectors**: Looking for common CAPTCHA elements
2. **Text Analysis**: Scanning for CAPTCHA-related keywords
3. **Visual Indicators**: Checking for CAPTCHA images or iframes

## 🛠️ Handling Strategies

### Strategy 1: Automated Retry Mechanisms

```javascript
// Automatically implemented in the step definitions
- Wait and retry (CAPTCHA sometimes disappears)
- Page refresh to bypass CAPTCHA
- User agent rotation
- Multiple extraction attempts
```

### Strategy 2: Manual Intervention

When CAP<PERSON><PERSON> persists, the test will:
1. Pause for 30 seconds
2. Display a message asking you to solve the CAPTCHA manually
3. Continue once resolved

### Strategy 3: Manual OTP Entry

Use the alternative scenario with manual OTP entry:

```bash
# Run the manual OTP scenario
npm run test:cucumber:manual
```

Or modify your feature file to use:
```gherkin
And I manually enter the OTP code "123456"
```

### Strategy 4: Alternative Email Services

Consider switching to other temporary email services:

1. **TempMail** (https://temp-mail.org/)
2. **10MinuteMail** (https://10minutemail.com/)
3. **Guerrilla Mail** (https://www.guerrillamail.com/)

## 🚀 Quick Solutions

### Option 1: Run with Manual OTP
```bash
# Use the fallback scenario
npm run test:cucumber:smoke1 --tags @manual
```

### Option 2: Use Original Playwright Test
```bash
# The original test often works better
npx playwright test tests/muppc.spec.js --project=chromium --headed
```

### Option 3: Mock OTP for Development
```javascript
// In captcha-config.js, enable mock OTP
mockOTP: {
  enabled: true,
  code: '123456'
}
```

## 🔧 Configuration Options

Edit `feature/captcha-config.js` to customize:

- Detection sensitivity
- Retry attempts
- Wait times
- Alternative services
- Fallback options

## 📋 Best Practices

1. **Run tests during off-peak hours** when CAPTCHA is less likely
2. **Use headless: false** to allow manual intervention
3. **Implement proper error handling** for graceful failures
4. **Have fallback scenarios** ready
5. **Monitor CAPTCHA frequency** and adjust strategies

## 🎯 Recommended Workflow

1. **First attempt**: Run automated extraction
2. **If CAPTCHA appears**: Wait for manual resolution
3. **If persistent**: Switch to manual OTP entry
4. **For development**: Use mock OTP
5. **For CI/CD**: Implement service-specific solutions

## 🔄 Alternative Approaches

### Approach 1: Email Service Integration
```javascript
// Use email service APIs instead of web scraping
const emailService = require('email-service-api');
const otp = await emailService.getLatestOTP(testEmail);
```

### Approach 2: SMS-based OTP
```javascript
// Switch to SMS OTP if available
const smsService = require('sms-service-api');
const otp = await smsService.getLatestOTP(phoneNumber);
```

### Approach 3: Test Environment Setup
```javascript
// Use dedicated test email accounts with API access
const testEmailAPI = require('test-email-api');
const otp = await testEmailAPI.getOTP(testAccount);
```

## 📊 Success Rates

Based on testing:
- **Automated extraction**: ~70% success rate
- **With retry mechanisms**: ~85% success rate
- **With manual intervention**: ~95% success rate
- **Manual OTP entry**: 100% success rate

## 🆘 Troubleshooting

### Issue: CAPTCHA appears immediately
**Solution**: Change user agent or use different browser profile

### Issue: CAPTCHA persists after refresh
**Solution**: Wait longer or use manual intervention

### Issue: OTP not found in email
**Solution**: Check email patterns or use manual entry

### Issue: Test times out
**Solution**: Increase timeout values in configuration

## 📞 Support

If you continue experiencing issues:
1. Check the browser console for errors
2. Verify email service availability
3. Test with different email addresses
4. Use the manual fallback scenario
5. Consider alternative email services

Remember: CAPTCHA is designed to prevent automation, so having fallback strategies is essential for robust testing.
