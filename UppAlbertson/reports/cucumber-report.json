[{"description": "  As a partner user\n  I want to login to the Albertsons Partner Portal\n  So that I can access the partner resources and tools", "elements": [{"description": "", "id": "albertsons-partner-portal-login-with-email-verification;successful-login-to-albertsons-partner-portal-with-email-verification", "keyword": "<PERSON><PERSON><PERSON>", "line": 11, "name": "Successful login to Albertsons Partner Portal with email verification", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": **********}}, {"arguments": [], "keyword": "Given ", "line": 7, "name": "I have a valid test email account \"<EMAIL>\"", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:111"}, "result": {"status": "passed", "duration": 382843}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "I have access to the YOPmail inbox \"gmvendorexternaleditordiv27\"", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:117"}, "result": {"status": "passed", "duration": 301907}}, {"arguments": [], "keyword": "Given ", "line": 12, "name": "I navigate to the Albertsons Partner Portal login page", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:122"}, "result": {"status": "passed", "duration": **********}}, {"arguments": [], "keyword": "When ", "line": 13, "name": "I enter my email address for authentication", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:128"}, "result": {"status": "passed", "duration": **********}}, {"arguments": [], "keyword": "And ", "line": 14, "name": "I click the Next button to proceed", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:133"}, "result": {"status": "passed", "duration": 196256994}}, {"arguments": [], "keyword": "Then ", "line": 15, "name": "I should be redirected to Microsoft authentication", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:138"}, "result": {"status": "passed", "duration": 1675108}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "I should receive a verification email", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:143"}, "result": {"status": "passed", "duration": 5001285450}}, {"arguments": [], "keyword": "When ", "line": 17, "name": "I open YOPmail to check my inbox", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:148"}, "result": {"status": "passed", "duration": 7964731954}}, {"arguments": [], "keyword": "And ", "line": 18, "name": "I find the verification email from Microsoft", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:165"}, "result": {"status": "passed", "duration": 413740761}}, {"arguments": [], "keyword": "And ", "line": 19, "name": "I extract the OTP code from the email", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:172"}, "result": {"status": "failed", "duration": 120001471252, "error_message": "Error: function timed out, ensure the promise resolves within 120000 milliseconds\n    at Timeout.<anonymous> (/Users/<USER>/Documents/UPP2025/UppAlbertson/node_modules/@cucumber/cucumber/lib/time.js:64:20)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "I return to the login page", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:246"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "I enter the OTP code", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:251"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 22, "name": "I click the verify button", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:310"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 23, "name": "I should be successfully logged into the Albertsons Partner Portal", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:318"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 24, "name": "I should see the portal dashboard", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:330"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 378874703}}], "tags": [{"name": "@login", "line": 10}, {"name": "@smoke1", "line": 10}], "type": "scenario"}], "id": "albertsons-partner-portal-login-with-email-verification", "line": 1, "keyword": "Feature", "name": "Albertsons Partner Portal Login with Email Verification", "tags": [], "uri": "feature/albertsons-login.feature"}]