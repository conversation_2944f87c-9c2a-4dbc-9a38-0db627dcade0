[{"description": "  As a partner user\n  I want to login to the Albertsons Partner Portal\n  So that I can access the partner resources and tools", "elements": [{"description": "", "id": "albertsons-partner-portal-login-with-email-verification;successful-login-to-albertsons-partner-portal-with-email-verification", "keyword": "<PERSON><PERSON><PERSON>", "line": 11, "name": "Successful login to Albertsons Partner Portal with email verification", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": **********}}, {"arguments": [], "keyword": "Given ", "line": 7, "name": "I have a valid test email account \"<EMAIL>\"", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:25"}, "result": {"status": "passed", "duration": 439838}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "I have access to the YOPmail inbox \"gmvendorexternaleditordiv27\"", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:31"}, "result": {"status": "passed", "duration": 253625}}, {"arguments": [], "keyword": "Given ", "line": 12, "name": "I navigate to the Albertsons Partner Portal login page", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:36"}, "result": {"status": "passed", "duration": **********}}, {"arguments": [], "keyword": "When ", "line": 13, "name": "I enter my email address for authentication", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:43"}, "result": {"status": "passed", "duration": ********}}, {"arguments": [], "keyword": "And ", "line": 14, "name": "I click the Next button to proceed", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:48"}, "result": {"status": "passed", "duration": 346848398}}, {"arguments": [], "keyword": "Then ", "line": 15, "name": "I should be redirected to Microsoft authentication", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:53"}, "result": {"status": "passed", "duration": 1573231}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "I should receive a verification email", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:58"}, "result": {"status": "passed", "duration": 5003523244}}, {"arguments": [], "keyword": "When ", "line": 17, "name": "I open YOPmail to check my inbox", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:63"}, "result": {"status": "passed", "duration": 7921367373}}, {"arguments": [], "keyword": "And ", "line": 18, "name": "I find the verification email from Microsoft", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:72"}, "result": {"status": "passed", "duration": 465210547}}, {"arguments": [], "keyword": "And ", "line": 19, "name": "I extract the OTP code from the email", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:79"}, "result": {"status": "failed", "duration": 3012571415, "error_message": "Error: OTP not found in email.\n    at World.<anonymous> (/Users/<USER>/Documents/UPP2025/UppAlbertson/feature/step_definitions/albertsons-login-steps.js:89:28)"}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "I return to the login page", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:93"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "I enter the OTP code", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:98"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 22, "name": "I click the verify button", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:125"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 23, "name": "I should be successfully logged into the Albertsons Partner Portal", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:133"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 24, "name": "I should see the portal dashboard", "match": {"location": "feature/step_definitions/albertsons-login-steps.js:145"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 458920260}}], "tags": [{"name": "@login", "line": 10}, {"name": "@smoke1", "line": 10}], "type": "scenario"}], "id": "albertsons-partner-portal-login-with-email-verification", "line": 1, "keyword": "Feature", "name": "Albertsons Partner Portal Login with Email Verification", "tags": [], "uri": "feature/albertsons-login.feature"}]