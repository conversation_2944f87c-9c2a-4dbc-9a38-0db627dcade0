const { Given, When, Then, Before, After, setDefaultTimeout } = require('@cucumber/cucumber');
const { chromium } = require('playwright');
const { expect } = require('@playwright/test');

setDefaultTimeout(120000); // Set step timeout to 2 minutes

let browser, context, page, yopmailPage;
let testEmail, yopmailInbox, extractedOTP;

// === CAPTCHA Handling Functions ===

async function detectCaptcha(page) {
  try {
    // Check for common CAPTCHA indicators
    const captchaSelectors = [
      'iframe[src*="recaptcha"]',
      '.g-recaptcha',
      '#captcha',
      '.captcha',
      'img[src*="captcha"]',
      '[data-sitekey]',
      '.cf-turnstile'
    ];

    for (const selector of captchaSelectors) {
      const element = await page.$(selector);
      if (element) {
        console.log(`CAPTCHA detected using selector: ${selector}`);
        return true;
      }
    }

    // Check for CAPTCHA-related text
    const pageText = await page.textContent('body');
    const captchaKeywords = ['captcha', 'verify you are human', 'robot', 'security check'];

    for (const keyword of captchaKeywords) {
      if (pageText.toLowerCase().includes(keyword)) {
        console.log(`CAPTCHA detected by keyword: ${keyword}`);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.log('Error detecting CAPTCHA:', error.message);
    return false;
  }
}

async function handleCaptcha(page) {
  console.log('🔄 Attempting CAPTCHA handling strategies...');

  // Strategy 1: Wait and retry (sometimes CAPTCHA disappears)
  console.log('Strategy 1: Waiting for CAPTCHA to potentially disappear...');
  await page.waitForTimeout(5000);

  if (!(await detectCaptcha(page))) {
    console.log('✅ CAPTCHA disappeared after waiting');
    return;
  }

  // Strategy 2: Refresh the page
  console.log('Strategy 2: Refreshing page to bypass CAPTCHA...');
  await page.reload();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForTimeout(3000);

  if (!(await detectCaptcha(page))) {
    console.log('✅ CAPTCHA bypassed after page refresh');
    return;
  }

  // Strategy 3: Try different user agent
  console.log('Strategy 3: Changing user agent...');
  await page.setExtraHTTPHeaders({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  });
  await page.reload();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForTimeout(3000);

  if (!(await detectCaptcha(page))) {
    console.log('✅ CAPTCHA bypassed after user agent change');
    return;
  }

  // Strategy 4: Manual intervention prompt
  console.log('⚠️ CAPTCHA still present. Manual intervention may be required.');
  console.log('🔍 Please check the browser window and solve the CAPTCHA if present.');

  // Wait for user to potentially solve CAPTCHA manually
  await page.waitForTimeout(30000); // 30 seconds for manual intervention
}

// === Setup and Teardown ===

Before(async function () {
  browser = await chromium.launch({ headless: false }); // Show browser
  context = await browser.newContext();
  page = await context.newPage();
});

After(async function () {
  if (browser) await browser.close(); // Close browser after each test
});

// === Step Definitions ===

// 1. Store test email address
Given('I have a valid test email account {string}', function (email) {
  testEmail = email;
  yopmailInbox = email.split('@')[0]; // Get part before '@'
});

// 2. Confirm YOPmail inbox
Given('I have access to the YOPmail inbox {string}', function (inbox) {
  yopmailInbox = inbox;
});

// 3. Navigate to the login page
Given('I navigate to the Albertsons Partner Portal login page', async function () {
  await page.goto('https://partner-uat.albertsons.com/memsp-ui-shell/meupp/');
  await page.waitForLoadState('domcontentloaded');
});

// 4. Enter email and click "Next"
When('I enter my email address for authentication', async function () {
  await page.waitForSelector('input[name="loginfmt"], input[type="email"]');
  await page.fill('input[name="loginfmt"], input[type="email"]', testEmail);
});

When('I click the Next button to proceed', async function () {
  await page.click('#idSIButton9');
});

// 5. Ensure redirect to Microsoft login
Then('I should be redirected to Microsoft authentication', async function () {
  expect(page.url()).toContain('login.microsoftonline.com');
});

// 6. Wait for email to be sent
Then('I should receive a verification email', async function () {
  await page.waitForTimeout(5000); // Wait for email to arrive
});

// 7. Open YOPmail and check inbox with CAPTCHA handling
When('I open YOPmail to check my inbox', async function () {
  yopmailPage = await context.newPage();
  await yopmailPage.goto('https://yopmail.com/en/');

  // Check for CAPTCHA before proceeding
  const captchaDetected = await detectCaptcha(yopmailPage);
  if (captchaDetected) {
    console.log('🚨 CAPTCHA detected on YOPmail. Attempting to handle...');
    await handleCaptcha(yopmailPage);
  }

  await yopmailPage.fill('#login', yopmailInbox);
  await yopmailPage.press('#login', 'Enter');
  await yopmailPage.waitForSelector('#ifinbox');
});

// 8. Find the latest email
When('I find the verification email from Microsoft', async function () {
  const inboxFrame = await (await yopmailPage.$('#ifinbox')).contentFrame();
  await inboxFrame.waitForSelector('div.m'); // Wait for email list
  await inboxFrame.locator('div.m').first().click(); // Open first email
});

// 9. Extract OTP from email content with enhanced error handling
When('I extract the OTP code from the email', async function () {
  let attempts = 0;
  const maxAttempts = 3;

  while (attempts < maxAttempts) {
    try {
      attempts++;
      console.log(`🔍 OTP extraction attempt ${attempts}/${maxAttempts}`);

      // Check for CAPTCHA on the email page
      const captchaDetected = await detectCaptcha(yopmailPage);
      if (captchaDetected) {
        console.log('🚨 CAPTCHA detected while reading email. Attempting to handle...');
        await handleCaptcha(yopmailPage);
      }

      const mailFrame = await (await yopmailPage.$('#ifmail')).contentFrame();
      await yopmailPage.waitForTimeout(3000); // Wait for content to load

      let mailText = await mailFrame.evaluate(() => document.body.innerText);
      console.log('📧 Email content preview:', mailText.substring(0, 200) + '...');

      // Try multiple OTP patterns
      const otpPatterns = [
        /code:\s*(\d{6,8})/i,
        /verification code:\s*(\d{6,8})/i,
        /code\s+is\s+(\d{6,8})/i,
        /your code:\s*(\d{6,8})/i,
        /(\d{6,8})/g
      ];

      for (const pattern of otpPatterns) {
        const match = mailText.match(pattern);
        if (match) {
          extractedOTP = match[1] || match[0];
          if (extractedOTP && extractedOTP.length >= 6 && extractedOTP.length <= 8) {
            console.log(`✅ OTP extracted successfully: ${extractedOTP}`);
            return; // Success, exit the function
          }
        }
      }

      if (attempts < maxAttempts) {
        console.log(`⚠️ OTP not found in attempt ${attempts}. Retrying...`);
        await yopmailPage.waitForTimeout(5000); // Wait before retry

        // Try refreshing the email
        await yopmailPage.reload();
        await yopmailPage.waitForLoadState('domcontentloaded');
        await yopmailPage.fill('#login', yopmailInbox);
        await yopmailPage.press('#login', 'Enter');
        await yopmailPage.waitForSelector('#ifinbox');

        // Click on the first email again
        const inboxFrame = await (await yopmailPage.$('#ifinbox')).contentFrame();
        await inboxFrame.waitForSelector('div.m');
        await inboxFrame.locator('div.m').first().click();
      }

    } catch (error) {
      console.log(`❌ Error in OTP extraction attempt ${attempts}:`, error.message);
      if (attempts === maxAttempts) {
        throw new Error(`Failed to extract OTP after ${maxAttempts} attempts. Last error: ${error.message}`);
      }
      await yopmailPage.waitForTimeout(3000); // Wait before retry
    }
  }

  if (!extractedOTP) {
    throw new Error('OTP not found in email after all attempts.');
  }
});

// 10. Go back to login page
When('I return to the login page', async function () {
  await page.bringToFront();
});

// 11. Enter the OTP code (automated)
When('I enter the OTP code', async function () {
  if (!extractedOTP) {
    throw new Error('No OTP available to enter. Please extract OTP first.');
  }

  console.log(`Entering OTP: ${extractedOTP}`);

  // Wait for OTP input field and fill it (try multiple selectors)
  try {
    await page.waitForSelector('input[name="otc"]', { timeout: 10000 });
    await page.fill('input[name="otc"]', extractedOTP);
    console.log('Successfully filled OTP using input[name="otc"]');
  } catch (error) {
    try {
      await page.waitForSelector('input[type="text"]', { timeout: 5000 });
      await page.fill('input[type="text"]', extractedOTP);
      console.log('Successfully filled OTP using input[type="text"]');
    } catch (error2) {
      try {
        await page.waitForSelector('input[placeholder*="code"], input[placeholder*="Code"]', { timeout: 5000 });
        await page.fill('input[placeholder*="code"], input[placeholder*="Code"]', extractedOTP);
        console.log('Successfully filled OTP using placeholder selector');
      } catch (error3) {
        console.log('Could not find OTP input field with any selector');
        throw new Error('OTP input field not found');
      }
    }
  }
});

// 11b. Manual OTP entry fallback
When('I manually enter the OTP code {string}', async function (manualOTP) {
  extractedOTP = manualOTP;
  console.log(`Manually entering OTP: ${extractedOTP}`);

  // Wait for OTP input field and fill it (try multiple selectors)
  try {
    await page.waitForSelector('input[name="otc"]', { timeout: 10000 });
    await page.fill('input[name="otc"]', extractedOTP);
    console.log('Successfully filled manual OTP using input[name="otc"]');
  } catch (error) {
    try {
      await page.waitForSelector('input[type="text"]', { timeout: 5000 });
      await page.fill('input[type="text"]', extractedOTP);
      console.log('Successfully filled manual OTP using input[type="text"]');
    } catch (error2) {
      try {
        await page.waitForSelector('input[placeholder*="code"], input[placeholder*="Code"]', { timeout: 5000 });
        await page.fill('input[placeholder*="code"], input[placeholder*="Code"]', extractedOTP);
        console.log('Successfully filled manual OTP using placeholder selector');
      } catch (error3) {
        console.log('Could not find OTP input field with any selector');
        throw new Error('OTP input field not found');
      }
    }
  }
});

// 12. Click the verify button
When('I click the verify button', async function () {
  console.log('Clicking verify button...');
  await page.waitForSelector('#idSIButton9', { timeout: 10000 });
  await page.click('#idSIButton9');
  console.log('Verify button clicked successfully');
});

// 13. Confirm successful login
Then('I should be successfully logged into the Albertsons Partner Portal', async function () {
  console.log('Verifying successful login...');

  // Wait for redirect back to Albertsons portal after successful authentication
  await page.waitForLoadState('domcontentloaded');
  console.log('After OTP verification, URL:', page.url());

  // Wait for successful login redirect to the portal
  await page.waitForURL('**/memsp-ui-shell/**', { timeout: 30000 });
  console.log('Successfully redirected to Albertsons portal:', page.url());
});

Then('I should see the portal dashboard', async function () {
  // Verify we're logged into the Albertsons Partner Portal
  await expect(page).toHaveURL(/partner-uat\.albertsons\.com\/memsp-ui-shell/);
  console.log('✅ Successfully logged into Albertsons Partner Portal!');

  await page.waitForTimeout(5000);

 
  
});





