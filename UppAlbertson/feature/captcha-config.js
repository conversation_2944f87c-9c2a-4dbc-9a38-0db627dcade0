// CAPTCHA Handling Configuration
// This file contains various strategies for handling CAPTCHA during automated testing

const captchaConfig = {
  // Detection settings
  detection: {
    selectors: [
      'iframe[src*="recaptcha"]',
      '.g-recaptcha',
      '#captcha',
      '.captcha',
      'img[src*="captcha"]',
      '[data-sitekey]',
      '.cf-turnstile',
      '.hcaptcha-box'
    ],
    keywords: [
      'captcha',
      'verify you are human',
      'robot',
      'security check',
      'prove you are not a robot',
      'i am not a robot'
    ],
    timeout: 5000
  },

  // Handling strategies
  strategies: {
    // Strategy 1: Wait and retry
    waitAndRetry: {
      enabled: true,
      waitTime: 5000,
      maxRetries: 3
    },

    // Strategy 2: Page refresh
    pageRefresh: {
      enabled: true,
      maxRefreshes: 2,
      waitAfterRefresh: 3000
    },

    // Strategy 3: User agent rotation
    userAgentRotation: {
      enabled: true,
      agents: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      ]
    },

    // Strategy 4: Manual intervention
    manualIntervention: {
      enabled: true,
      waitTime: 30000, // 30 seconds
      promptMessage: '🔍 Please check the browser window and solve the CAPTCHA if present.'
    },

    // Strategy 5: Alternative email services
    alternativeServices: {
      enabled: true,
      services: [
        {
          name: 'TempMail',
          url: 'https://temp-mail.org/',
          inboxSelector: '#mail',
          emailListSelector: '.mail-item'
        },
        {
          name: '10MinuteMail',
          url: 'https://10minutemail.com/',
          inboxSelector: '#mailAddress',
          emailListSelector: '.message'
        }
      ]
    }
  },

  // Fallback options
  fallbacks: {
    // Use mock OTP for testing
    mockOTP: {
      enabled: false, // Set to true for testing
      code: '123456'
    },

    // Manual OTP entry
    manualEntry: {
      enabled: true,
      promptTimeout: 60000 // 1 minute
    },

    // Skip email verification (if possible)
    skipVerification: {
      enabled: false,
      alternativeFlow: 'password_based'
    }
  }
};

module.exports = captchaConfig;
